#include "uart_passthrough.h"
#include "watchdog_config.h"
#include "esp_compat.h"    // ESP-IDF版本兼容性支持
#include "esp_log.h"
#include "esp_timer.h"
#include "string.h"
#include <inttypes.h>

static const char *TAG = "UART_PASSTHROUGH";

// 透传统计信息
typedef struct {
    uint32_t tx_bytes;
    uint32_t rx_bytes;
} uart_stats_t;

static uart_stats_t uart_stats[UART_NUM_MAX + 1]; // +1 for software UART
static TaskHandle_t hardware_uart_tasks[UART_NUM_MAX];

// 软件UART位时序参数 (115200 baud)
#define SOFT_UART_BIT_TIME_US   (1000000 / 115200)  // 约8.68微秒

/**
 * @brief 高精度微秒级延时函数 - 专为Dedicated GPIO优化
 * 使用esp_timer获得更精确的延时
 */
static inline void IRAM_ATTR delay_us_precise(uint32_t us)
{
    if (us == 0) return;

    if (us <= 3) {
        // 极短延时，使用CPU循环
        volatile uint32_t cycles = us * 80;  // 根据实际CPU频率调整
        while (cycles--) {
            __asm__ __volatile__("nop");
        }
    } else if (us <= 100) {
        // 短延时，使用高精度定时器
        uint64_t start = esp_timer_get_time();
        while ((esp_timer_get_time() - start) < us) {
            __asm__ __volatile__("nop");
        }
    } else {
        // 长延时，使用任务延时
        TickType_t ticks = pdMS_TO_TICKS((us + 999) / 1000);
        if (ticks == 0) ticks = 1;
        vTaskDelay(ticks);
    }
}

/**
 * @brief 非阻塞检查起始位
 * @return true 检测到起始位，false 未检测到
 */
static inline bool IRAM_ATTR check_start_bit_non_blocking(void)
{
    // 简单检查当前GPIO状态，不等待
    return !(dedic_gpio_cpu_ll_read_in() & g_soft_uart.rx_mask);
}

/**
 * @brief 发送单个字节 - 使用Dedicated GPIO
 */
static void IRAM_ATTR soft_uart_send_byte(uint8_t byte)
{
    // 进入临界区
    portMUX_TYPE mux = portMUX_INITIALIZER_UNLOCKED;
    taskENTER_CRITICAL(&mux);

    // 发送起始位 (0)
    dedic_gpio_cpu_ll_write_mask(g_soft_uart.tx_mask, 0);
    delay_us_precise(g_soft_uart.bit_time_us);

    // 发送8个数据位 (LSB first)
    for (int i = 0; i < 8; i++) {
        if (byte & (1 << i)) {
            dedic_gpio_cpu_ll_write_mask(g_soft_uart.tx_mask, g_soft_uart.tx_mask);
        } else {
            dedic_gpio_cpu_ll_write_mask(g_soft_uart.tx_mask, 0);
        }
        delay_us_precise(g_soft_uart.bit_time_us);
    }

    // 发送停止位 (1)
    dedic_gpio_cpu_ll_write_mask(g_soft_uart.tx_mask, g_soft_uart.tx_mask);
    delay_us_precise(g_soft_uart.bit_time_us);

    taskEXIT_CRITICAL(&mux);

    g_soft_uart.tx_bytes++;
}

/**
 * @brief 接收单个字节 - 使用Dedicated GPIO (看门狗友好版本)
 * @return 接收到的字节，如果超时返回-1
 */
static int IRAM_ATTR soft_uart_receive_byte(uint32_t timeout_us)
{
    uint64_t start_time = esp_timer_get_time();
    uint64_t last_yield_time = start_time;
    uint64_t last_wdt_reset_time = start_time;
    const uint64_t yield_interval_us = 500; // 每500us让出一次CPU，提高响应性
    const uint64_t wdt_reset_interval_us = 1000000; // 每1秒重置一次看门狗

    // 等待起始位 (下降沿) - 看门狗友好版本
    while (dedic_gpio_cpu_ll_read_in() & g_soft_uart.rx_mask) {
        uint64_t current_time = esp_timer_get_time();

        // 检查超时
        if ((current_time - start_time) > timeout_us) {
            return -1; // 超时
        }

        // 定期让出CPU，避免看门狗超时
        if ((current_time - last_yield_time) > yield_interval_us) {
            // 使用taskYIELD()让出CPU，比延时更高效
            taskYIELD();
            last_yield_time = current_time;
        }

        // 定期重置看门狗，防止长时间等待导致超时
        if ((current_time - last_wdt_reset_time) > wdt_reset_interval_us) {
            watchdog_reset();
            last_wdt_reset_time = current_time;
        }

        __asm__ __volatile__("nop");
    }

    // 使用更短的临界区，减少看门狗超时风险
    portMUX_TYPE mux = portMUX_INITIALIZER_UNLOCKED;

    // 等待半个位时间，到达起始位中心
    delay_us_precise(g_soft_uart.bit_time_us / 2);

    // 进入临界区进行快速采样
    taskENTER_CRITICAL(&mux);

    // 确认起始位
    if (dedic_gpio_cpu_ll_read_in() & g_soft_uart.rx_mask) {
        taskEXIT_CRITICAL(&mux);
        g_soft_uart.rx_errors++;
        return -1; // 假起始位
    }

    uint8_t rx_byte = 0;

    // 接收8个数据位 - 在临界区内快速完成
    for (int i = 0; i < 8; i++) {
        // 退出临界区进行延时，避免长时间阻塞
        taskEXIT_CRITICAL(&mux);
        delay_us_precise(g_soft_uart.bit_time_us);
        taskENTER_CRITICAL(&mux);

        if (dedic_gpio_cpu_ll_read_in() & g_soft_uart.rx_mask) {
            rx_byte |= (1 << i);
        }
    }

    // 退出临界区进行停止位延时
    taskEXIT_CRITICAL(&mux);
    delay_us_precise(g_soft_uart.bit_time_us);
    taskENTER_CRITICAL(&mux);

    // 检查停止位
    bool stop_bit_valid = (dedic_gpio_cpu_ll_read_in() & g_soft_uart.rx_mask) != 0;

    taskEXIT_CRITICAL(&mux);

    if (!stop_bit_valid) {
        g_soft_uart.rx_errors++;
        return -1; // 帧错误
    }

    g_soft_uart.rx_bytes++;
    return rx_byte;
}

/**
 * @brief 初始化串口透传模块
 */
esp_err_t uart_passthrough_init(void)
{
    ESP_LOGI(TAG, "Initializing UART passthrough module...");
    
    // 初始化统计信息
    memset(uart_stats, 0, sizeof(uart_stats));
    memset(hardware_uart_tasks, 0, sizeof(hardware_uart_tasks));
    
    // 初始化硬件UART2 (UART_PASSTHROUGH_1)
    esp_err_t ret = uart_passthrough_init_hardware_uart(
        UART_PASSTHROUGH_1, 
        PASSTHROUGH1_TXD, 
        PASSTHROUGH1_RXD
    );
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize hardware UART2");
        return ret;
    }
    
    // 初始化软件UART (UART3替代) - 可选功能
#if ENABLE_SOFTWARE_UART
    ret = uart_passthrough_init_software_uart(PASSTHROUGH2_TXD, PASSTHROUGH2_RXD);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize software UART");
        return ret;
    }
#else
    ESP_LOGW(TAG, "Software UART disabled for debugging");
#endif
    
    ESP_LOGI(TAG, "UART passthrough module initialized");
    return ESP_OK;
}

/**
 * @brief 初始化硬件UART
 */
esp_err_t uart_passthrough_init_hardware_uart(uart_port_t uart_num, gpio_num_t tx_pin, gpio_num_t rx_pin)
{
    if (uart_num >= UART_NUM_MAX) {
        return ESP_ERR_INVALID_ARG;
    }
    
    const uart_config_t uart_config = {
        .baud_rate = 115200,
        .data_bits = UART_DATA_8_BITS,
        .parity = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE,
        .source_clk = UART_SCLK_DEFAULT,
    };
    
    esp_err_t ret = uart_driver_install(uart_num, RX_BUF_SIZE * 2, TX_BUF_SIZE * 2, 0, NULL, 0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to install UART%d driver: %s", uart_num, esp_err_to_name(ret));
        return ret;
    }
    
    ret = uart_param_config(uart_num, &uart_config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to configure UART%d: %s", uart_num, esp_err_to_name(ret));
        return ret;
    }
    
    ret = uart_set_pin(uart_num, tx_pin, rx_pin, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to set UART%d pins: %s", uart_num, esp_err_to_name(ret));
        return ret;
    }
    
    ESP_LOGI(TAG, "Hardware UART%d initialized on TX:%d, RX:%d", uart_num, tx_pin, rx_pin);
    return ESP_OK;
}

/**
 * @brief 初始化软件UART - 基于Dedicated GPIO
 */
esp_err_t uart_passthrough_init_software_uart(gpio_num_t tx_pin, gpio_num_t rx_pin)
{
    esp_err_t ret = ESP_OK;

    // 初始化软件UART结构体基本信息
    g_soft_uart.tx_pin = tx_pin;
    g_soft_uart.rx_pin = rx_pin;
    g_soft_uart.baud_rate = 115200;
    g_soft_uart.bit_time_us = 1000000 / g_soft_uart.baud_rate; // 约8.68微秒
    g_soft_uart.tx_bytes = 0;
    g_soft_uart.rx_bytes = 0;
    g_soft_uart.rx_errors = 0;

    // 创建队列
    g_soft_uart.tx_queue = xQueueCreate(256, sizeof(uint8_t));
    g_soft_uart.rx_queue = xQueueCreate(256, sizeof(uint8_t));

    if (g_soft_uart.tx_queue == NULL || g_soft_uart.rx_queue == NULL) {
        ESP_LOGE(TAG, "Failed to create software UART queues");
        return ESP_FAIL;
    }

    // 配置TX Dedicated GPIO Bundle
    int tx_gpio_array[] = {tx_pin};
    dedic_gpio_bundle_config_t tx_config = {
        .gpio_array = tx_gpio_array,
        .array_size = 1,
        .flags = {
            .out_en = 1,
            .out_invert = 0,
        },
    };

    ret = dedic_gpio_new_bundle(&tx_config, &g_soft_uart.tx_bundle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create TX dedicated GPIO bundle: %s", esp_err_to_name(ret));
        goto cleanup;
    }

    // 获取TX mask
    ret = dedic_gpio_get_out_mask(g_soft_uart.tx_bundle, &g_soft_uart.tx_mask);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to get TX mask: %s", esp_err_to_name(ret));
        goto cleanup;
    }

    // 配置RX Dedicated GPIO Bundle
    int rx_gpio_array[] = {rx_pin};
    dedic_gpio_bundle_config_t rx_config = {
        .gpio_array = rx_gpio_array,
        .array_size = 1,
        .flags = {
            .in_en = 1,
            .in_invert = 0,
        },
    };

    ret = dedic_gpio_new_bundle(&rx_config, &g_soft_uart.rx_bundle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create RX dedicated GPIO bundle: %s", esp_err_to_name(ret));
        goto cleanup;
    }

    // 获取RX mask
    ret = dedic_gpio_get_in_mask(g_soft_uart.rx_bundle, &g_soft_uart.rx_mask);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to get RX mask: %s", esp_err_to_name(ret));
        goto cleanup;
    }

    // 设置TX引脚初始状态为高电平（UART空闲状态）
    dedic_gpio_bundle_write(g_soft_uart.tx_bundle, 0x01, 0x01);

    g_soft_uart.initialized = true;
    ESP_LOGI(TAG, "Dedicated GPIO Software UART initialized on TX:%d, RX:%d, baud:%" PRIu32,
             tx_pin, rx_pin, g_soft_uart.baud_rate);
    ESP_LOGI(TAG, "TX mask: 0x%08" PRIX32 ", RX mask: 0x%08" PRIX32, g_soft_uart.tx_mask, g_soft_uart.rx_mask);

    return ESP_OK;

cleanup:
    if (g_soft_uart.tx_bundle) {
        dedic_gpio_del_bundle(g_soft_uart.tx_bundle);
        g_soft_uart.tx_bundle = NULL;
    }
    if (g_soft_uart.rx_bundle) {
        dedic_gpio_del_bundle(g_soft_uart.rx_bundle);
        g_soft_uart.rx_bundle = NULL;
    }
    if (g_soft_uart.tx_queue) {
        vQueueDelete(g_soft_uart.tx_queue);
        g_soft_uart.tx_queue = NULL;
    }
    if (g_soft_uart.rx_queue) {
        vQueueDelete(g_soft_uart.rx_queue);
        g_soft_uart.rx_queue = NULL;
    }
    return ret;
}

/**
 * @brief 硬件UART透传任务
 */
void uart_passthrough_hardware_task(void* pvParameters)
{
    uart_port_t uart_num = (uart_port_t)(int)pvParameters;
    char task_name[32];
    snprintf(task_name, sizeof(task_name), "UART%d_PASSTHROUGH", uart_num);

    ESP_LOGI(TAG, "%s task started", task_name);

    // 将任务添加到看门狗监控
    esp_err_t wdt_ret = watchdog_add_current_task(task_name);
    if (wdt_ret != ESP_OK) {
        ESP_LOGW(TAG, "Failed to add %s task to watchdog, continuing anyway", task_name);
    }

    uint8_t rx_buffer[RX_BUF_SIZE];
    uint32_t wdt_reset_counter = 0;
    const uint32_t wdt_reset_interval = 100; // 每100次循环重置一次看门狗

    while (1) {
        // 定期重置看门狗
        wdt_reset_counter++;
        if (wdt_reset_counter >= wdt_reset_interval) {
            watchdog_reset();
            wdt_reset_counter = 0;
        }

        // 从UART接收数据 - 使用较短超时避免长时间阻塞
        int rx_len = uart_read_bytes(uart_num, rx_buffer, RX_BUF_SIZE, pdMS_TO_TICKS(50));

        if (rx_len > 0) {
            // 更新接收统计
            uart_stats[uart_num].rx_bytes += rx_len;

            ESP_LOGD(TAG, "%s received %d bytes", task_name, rx_len);

            // 接收到数据后立即重置看门狗
            watchdog_reset();

            // 立即回传数据
            int tx_len = uart_write_bytes(uart_num, rx_buffer, rx_len);
            if (tx_len > 0) {
                uart_stats[uart_num].tx_bytes += tx_len;
                ESP_LOGD(TAG, "%s echoed %d bytes", task_name, tx_len);
            } else {
                ESP_LOGW(TAG, "%s failed to echo data", task_name);
            }
        }

        vTaskDelay(pdMS_TO_TICKS(10)); // 短暂延时，避免占用过多CPU
    }
}

/**
 * @brief 软件UART发送任务 - 基于Dedicated GPIO
 */
void uart_passthrough_software_tx_task(void* pvParameters)
{
    ESP_LOGI(TAG, "Dedicated GPIO Software UART TX task started");

    while (1) {
        uint8_t tx_byte;

        // 从发送队列获取数据
        if (xQueueReceive(g_soft_uart.tx_queue, &tx_byte, portMAX_DELAY) == pdTRUE) {
            // 使用优化的发送函数
            soft_uart_send_byte(tx_byte);
        }
    }
}

/**
 * @brief 软件UART接收任务 - 基于Dedicated GPIO
 */
void uart_passthrough_software_rx_task(void* pvParameters)
{
    ESP_LOGI(TAG, "Dedicated GPIO Software UART RX task started");

    // 将任务添加到看门狗监控
    esp_err_t wdt_ret = watchdog_add_current_task("soft_uart_rx");
    if (wdt_ret != ESP_OK) {
        ESP_LOGW(TAG, "Failed to add task to watchdog, continuing anyway");
    }

    TickType_t last_wake_time = xTaskGetTickCount();
    // 确保check_period至少为1个tick，避免xTaskDelayUntil断言失败
    TickType_t check_period = pdMS_TO_TICKS(5); // 减少到5ms检查周期，提高响应性
    if (check_period == 0) {
        check_period = 1; // 至少1个tick
    }
    uint32_t consecutive_idle_count = 0;
    const uint32_t max_idle_before_sleep = 20; // 增加到20次空闲后延长休眠
    uint32_t wdt_reset_counter = 0;
    const uint32_t wdt_reset_interval = 100; // 每100次循环重置一次看门狗

    while (1) {
        // 定期重置看门狗
        wdt_reset_counter++;
        if (wdt_reset_counter >= wdt_reset_interval) {
            watchdog_reset();
            wdt_reset_counter = 0;
        }

        // 首先非阻塞检查是否有起始位
        if (check_start_bit_non_blocking()) {
            // 检测到起始位，尝试接收完整字节
            int rx_byte = soft_uart_receive_byte(2000); // 增加到2ms超时，减少丢包

            if (rx_byte >= 0) {
                consecutive_idle_count = 0; // 重置空闲计数

                // 将接收到的数据放入接收队列
                if (xQueueSend(g_soft_uart.rx_queue, &rx_byte, 0) == pdTRUE) {
                    // 立即回传数据（透传功能）
                    xQueueSend(g_soft_uart.tx_queue, &rx_byte, 0);
                }

                ESP_LOGD(TAG, "Software UART received byte: 0x%02X", rx_byte);

                // 接收到数据后立即重置看门狗
                watchdog_reset();
            }
        } else {
            // 没有检测到起始位，增加空闲计数
            consecutive_idle_count++;

            // 根据空闲时间调整休眠策略
            if (consecutive_idle_count > max_idle_before_sleep) {
                // 长时间空闲，延长休眠时间并重置看门狗
                watchdog_friendly_delay(30, true); // 30ms看门狗友好休眠
                consecutive_idle_count = 0; // 重置计数
            } else {
                // 短暂休眠，保持响应性
                vTaskDelayUntil(&last_wake_time, check_period);
            }
        }
    }
}

/**
 * @brief 启动串口透传任务
 */
esp_err_t uart_passthrough_start_tasks(void)
{
    ESP_LOGI(TAG, "Starting UART passthrough tasks...");

    // 启动硬件UART透传任务
    BaseType_t ret = xTaskCreate(
        uart_passthrough_hardware_task,
        "uart2_passthrough",
        UART_TASK_STACK_SIZE,
        (void*)UART_PASSTHROUGH_1,
        UART_TASK_PRIORITY,
        &hardware_uart_tasks[UART_PASSTHROUGH_1]
    );

    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create UART2 passthrough task");
        return ESP_FAIL;
    }

    // 启动软件UART任务 - 可选功能
#if ENABLE_SOFTWARE_UART
    // 启动软件UART发送任务 - 使用更大的栈空间
    ESP_LOGI(TAG, "Creating software UART TX task with stack size: %d", UART_TASK_STACK_SIZE);
    ret = xTaskCreate(
        uart_passthrough_software_tx_task,
        "soft_uart_tx",
        UART_TASK_STACK_SIZE,
        NULL,
        UART_TASK_PRIORITY,
        &g_soft_uart.tx_task_handle
    );

    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create software UART TX task");
        return ESP_FAIL;
    }

    // 启动软件UART接收任务 - 使用更大的栈空间
    ESP_LOGI(TAG, "Creating software UART RX task with stack size: %d", UART_TASK_STACK_SIZE);
    ret = xTaskCreate(
        uart_passthrough_software_rx_task,
        "soft_uart_rx",
        UART_TASK_STACK_SIZE,
        NULL,
        UART_TASK_PRIORITY,
        &g_soft_uart.rx_task_handle
    );

    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create software UART RX task");
        return ESP_FAIL;
    }
#else
    ESP_LOGW(TAG, "Software UART tasks disabled for debugging");
#endif

    ESP_LOGI(TAG, "All UART passthrough tasks started");
    return ESP_OK;
}

/**
 * @brief 停止串口透传任务
 */
void uart_passthrough_stop_tasks(void)
{
    // 停止硬件UART任务
    for (int i = 0; i < UART_NUM_MAX; i++) {
        if (hardware_uart_tasks[i] != NULL) {
            vTaskDelete(hardware_uart_tasks[i]);
            hardware_uart_tasks[i] = NULL;
        }
    }

    // 停止软件UART任务
    if (g_soft_uart.tx_task_handle != NULL) {
        vTaskDelete(g_soft_uart.tx_task_handle);
        g_soft_uart.tx_task_handle = NULL;
    }

    if (g_soft_uart.rx_task_handle != NULL) {
        vTaskDelete(g_soft_uart.rx_task_handle);
        g_soft_uart.rx_task_handle = NULL;
    }

    ESP_LOGI(TAG, "All UART passthrough tasks stopped");
}

/**
 * @brief 软件UART发送数据
 */
int uart_passthrough_software_send(const uint8_t* data, int len)
{
    if (!g_soft_uart.initialized || data == NULL || len <= 0) {
        return 0;
    }

    int sent = 0;
    for (int i = 0; i < len; i++) {
        if (xQueueSend(g_soft_uart.tx_queue, &data[i], pdMS_TO_TICKS(100)) == pdTRUE) {
            sent++;
        } else {
            ESP_LOGW(TAG, "Software UART TX queue full");
            break;
        }
    }

    return sent;
}

/**
 * @brief 软件UART接收数据
 */
int uart_passthrough_software_receive(uint8_t* buffer, int max_len, int timeout_ms)
{
    if (!g_soft_uart.initialized || buffer == NULL || max_len <= 0) {
        return 0;
    }

    int received = 0;
    TickType_t timeout_ticks = pdMS_TO_TICKS(timeout_ms);

    for (int i = 0; i < max_len; i++) {
        if (xQueueReceive(g_soft_uart.rx_queue, &buffer[i], timeout_ticks) == pdTRUE) {
            received++;
            timeout_ticks = pdMS_TO_TICKS(10); // 后续字节使用较短超时
        } else {
            break;
        }
    }

    return received;
}

/**
 * @brief 获取透传统计信息
 */
esp_err_t uart_passthrough_get_stats(uart_port_t uart_num, uint32_t* tx_bytes, uint32_t* rx_bytes)
{
    if (tx_bytes == NULL || rx_bytes == NULL) {
        return ESP_ERR_INVALID_ARG;
    }

    if (uart_num == UART_NUM_MAX) {
        // 软串口统计信息
        *tx_bytes = g_soft_uart.tx_bytes;
        *rx_bytes = g_soft_uart.rx_bytes;
    } else {
        // 硬件UART统计信息
        if (uart_num < 0 || uart_num >= UART_NUM_MAX) {
            return ESP_ERR_INVALID_ARG;
        }
        *tx_bytes = uart_stats[uart_num].tx_bytes;
        *rx_bytes = uart_stats[uart_num].rx_bytes;
    }

    return ESP_OK;
}

/**
 * @brief 重置透传统计信息
 */
void uart_passthrough_reset_stats(void)
{
    memset(uart_stats, 0, sizeof(uart_stats));

    // 重置软串口统计信息
    g_soft_uart.tx_bytes = 0;
    g_soft_uart.rx_bytes = 0;
    g_soft_uart.rx_errors = 0;

    ESP_LOGI(TAG, "UART passthrough statistics reset");
}

/**
 * @brief 获取软串口错误统计信息
 */
uint32_t uart_passthrough_get_soft_uart_errors(void)
{
    return g_soft_uart.rx_errors;
}

/**
 * @brief 去初始化串口透传模块
 */
void uart_passthrough_deinit(void)
{
    // 停止所有任务
    uart_passthrough_stop_tasks();

    // 卸载硬件UART驱动
    uart_driver_delete(UART_PASSTHROUGH_1);

    // 清理软件UART资源
    if (g_soft_uart.tx_bundle != NULL) {
        dedic_gpio_del_bundle(g_soft_uart.tx_bundle);
        g_soft_uart.tx_bundle = NULL;
    }

    if (g_soft_uart.rx_bundle != NULL) {
        dedic_gpio_del_bundle(g_soft_uart.rx_bundle);
        g_soft_uart.rx_bundle = NULL;
    }

    if (g_soft_uart.tx_queue != NULL) {
        vQueueDelete(g_soft_uart.tx_queue);
        g_soft_uart.tx_queue = NULL;
    }

    if (g_soft_uart.rx_queue != NULL) {
        vQueueDelete(g_soft_uart.rx_queue);
        g_soft_uart.rx_queue = NULL;
    }

    g_soft_uart.initialized = false;

    ESP_LOGI(TAG, "UART passthrough module deinitialized");
}
