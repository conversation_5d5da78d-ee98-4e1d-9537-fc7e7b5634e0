#include "uboot_operations.h"
#include "usb_otg.h"
#include "watchdog_config.h"
#include "esp_log.h"
#include "string.h"

static const char *TAG = "UBOOT_OPS";

// 最后一次错误信息
static char last_error_message[256] = {0};

/**
 * @brief 设置错误信息
 */
static void set_last_error(const char* error_msg)
{
    if (error_msg != NULL) {
        strncpy(last_error_message, error_msg, sizeof(last_error_message) - 1);
        last_error_message[sizeof(last_error_message) - 1] = '\0';
    }
}

/**
 * @brief 初始化U-Boot操作模块
 */
esp_err_t uboot_operations_init(void)
{
    ESP_LOGI(TAG, "Initializing U-Boot operations module...");
    
    // 清空错误信息
    memset(last_error_message, 0, sizeof(last_error_message));
    
    ESP_LOGI(TAG, "U-Boot operations module initialized");
    return ESP_OK;
}

/**
 * @brief 清空UART接收缓冲区
 */
void uboot_clear_uart_buffer(uart_port_t uart_num)
{
    uint8_t temp_buffer[256];
    while (uart_read_bytes(uart_num, temp_buffer, sizeof(temp_buffer), 0) > 0) {
        // 清空缓冲区
    }
}

/**
 * @brief 发送U-Boot命令并等待响应
 */
esp_err_t uboot_send_command(const char* command, const char* expected_response, 
                             int timeout_ms, char* response_buffer, int buffer_size)
{
    if (command == NULL) {
        set_last_error("Invalid command parameter");
        return ESP_ERR_INVALID_ARG;
    }
    
    ESP_LOGI(TAG, "Sending command: %s", command);
    
    // 清空接收缓冲区
    uboot_clear_uart_buffer(UART_DEBUG);
    
    // 发送命令
    int cmd_len = strlen(command);
    int sent = uart_write_bytes(UART_DEBUG, command, cmd_len);
    if (sent != cmd_len) {
        set_last_error("Failed to send complete command");
        return ESP_FAIL;
    }
    
    // 发送回车换行
    uart_write_bytes(UART_DEBUG, "\r\n", 2);
    
    // 等待响应
    if (expected_response != NULL || response_buffer != NULL) {
        return uboot_wait_for_string(expected_response, timeout_ms, response_buffer, buffer_size);
    }
    
    return ESP_OK;
}

/**
 * @brief 等待特定字符串出现
 */
esp_err_t uboot_wait_for_string(const char* expected_string, int timeout_ms,
                                char* response_buffer, int buffer_size)
{
    if (response_buffer != NULL && buffer_size > 0) {
        memset(response_buffer, 0, buffer_size);
    }
    
    uint8_t rx_buffer[RX_BUF_SIZE];
    int total_received = 0;
    uint32_t start_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
    
    uint32_t wdt_reset_counter = 0;
    const uint32_t wdt_reset_interval = 10; // 每10次循环重置一次看门狗

    while (1) {
        uint32_t current_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
        if (current_time - start_time > timeout_ms) {
            set_last_error("Timeout waiting for response");
            ESP_LOGW(TAG, "Timeout waiting for: %s", expected_string ? expected_string : "response");
            return ESP_ERR_TIMEOUT;
        }

        // 定期重置看门狗，防止长时间等待导致超时
        wdt_reset_counter++;
        if (wdt_reset_counter >= wdt_reset_interval) {
            watchdog_reset();
            wdt_reset_counter = 0;
        }

        // 使用较短的超时时间，避免长时间阻塞
        int rx_len = uart_read_bytes(UART_DEBUG, rx_buffer, RX_BUF_SIZE - 1, pdMS_TO_TICKS(50));
        if (rx_len > 0) {
            rx_buffer[rx_len] = '\0';
            
            // 保存响应到缓冲区
            if (response_buffer != NULL && buffer_size > 0) {
                int remaining = buffer_size - total_received - 1;
                if (remaining > 0) {
                    int copy_len = (rx_len < remaining) ? rx_len : remaining;
                    memcpy(response_buffer + total_received, rx_buffer, copy_len);
                    total_received += copy_len;
                    response_buffer[total_received] = '\0';
                }
            }
            
            ESP_LOGD(TAG, "Received: %s", rx_buffer);
            
            // 检查是否包含期望的字符串
            if (expected_string != NULL && strstr((char*)rx_buffer, expected_string) != NULL) {
                ESP_LOGI(TAG, "Found expected string: %s", expected_string);
                return ESP_OK;
            }
            
            // 重置超时计时器（有数据接收时）
            start_time = current_time;
        }
        
        vTaskDelay(pdMS_TO_TICKS(10));
    }
}

/**
 * @brief 测试Debug串口登录
 */
esp_err_t uboot_test_debug_login(void)
{
    ESP_LOGI(TAG, "Testing debug login...");
    
    char response_buffer[512];
    
    // 发送root登录命令
    esp_err_t ret = uboot_send_command("root", LINUX_ROOT_PROMPT, 
                                       DEBUG_LOGIN_TIMEOUT, response_buffer, sizeof(response_buffer));
    
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "Debug login test successful");
        ESP_LOGD(TAG, "Login response: %s", response_buffer);
    } else {
        ESP_LOGE(TAG, "Debug login test failed");
        ESP_LOGD(TAG, "Login response: %s", response_buffer);
    }
    
    return ret;
}

/**
 * @brief 执行Linux登录流程
 */
esp_err_t uboot_linux_login(void)
{
    ESP_LOGI(TAG, "Executing Linux login...");
    
    // 发送root用户名
    esp_err_t ret = uboot_send_command("root", NULL, 3000, NULL, 0);
    if (ret != ESP_OK) {
        set_last_error("Failed to send root username");
        return ret;
    }
    
    // 等待命令行提示符
    ret = uboot_wait_for_string(LINUX_ROOT_PROMPT, 5000, NULL, 0);
    if (ret != ESP_OK) {
        set_last_error("Failed to get Linux prompt after login");
        return ret;
    }
    
    ESP_LOGI(TAG, "Linux login completed");
    return ESP_OK;
}

/**
 * @brief 执行系统重启
 */
esp_err_t uboot_system_reboot(void)
{
    ESP_LOGI(TAG, "Executing system reboot...");
    
    esp_err_t ret = uboot_send_command("reboot", NULL, 1000, NULL, 0);
    if (ret != ESP_OK) {
        set_last_error("Failed to send reboot command");
        return ret;
    }
    
    ESP_LOGI(TAG, "Reboot command sent");
    return ESP_OK;
}

/**
 * @brief 进入U-Boot命令行模式
 */
esp_err_t uboot_enter_command_mode(void)
{
    ESP_LOGI(TAG, "Entering U-Boot command mode...");
    
    // 等待自动启动提示
    esp_err_t ret = uboot_wait_for_string(UBOOT_AUTOBOOT_PROMPT, 10000, NULL, 0);
    if (ret != ESP_OK) {
        set_last_error("Failed to detect autoboot prompt");
        return ret;
    }
    
    // 发送回车阻止自动启动
    ret = uboot_send_command("", UBOOT_PROMPT, 3000, NULL, 0);
    if (ret != ESP_OK) {
        set_last_error("Failed to enter U-Boot command mode");
        return ret;
    }
    
    ESP_LOGI(TAG, "Entered U-Boot command mode");
    return ESP_OK;
}

/**
 * @brief 启动UMS（USB Mass Storage）模式
 */
esp_err_t uboot_start_ums_mode(void)
{
    ESP_LOGI(TAG, "Starting UMS mode...");
    
    esp_err_t ret = uboot_send_command(UBOOT_UMS_COMMAND, NULL, 2000, NULL, 0);
    if (ret != ESP_OK) {
        set_last_error("Failed to start UMS mode");
        return ret;
    }
    
    ESP_LOGI(TAG, "UMS mode started");
    return ESP_OK;
}

/**
 * @brief 发送Ctrl-C中断信号
 */
esp_err_t uboot_send_ctrl_c(void)
{
    ESP_LOGI(TAG, "Sending Ctrl-C interrupt...");

    uint8_t ctrl_c = 0x03;
    int sent = uart_write_bytes(UART_DEBUG, &ctrl_c, 1);

    if (sent != 1) {
        set_last_error("Failed to send Ctrl-C");
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "Ctrl-C sent");
    return ESP_OK;
}

/**
 * @brief 退出UMS模式
 */
esp_err_t uboot_exit_ums_mode(void)
{
    ESP_LOGI(TAG, "Exiting UMS mode...");

    // 发送Ctrl-C中断UMS模式
    esp_err_t ret = uboot_send_ctrl_c();
    if (ret != ESP_OK) {
        return ret;
    }

    // 等待U-Boot提示符或超时
    ret = uboot_wait_for_string(UBOOT_PROMPT, 1000, NULL, 0);
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "Did not receive U-Boot prompt after Ctrl-C, continuing...");
        // 不返回错误，因为可能已经退出了UMS模式
    }

    ESP_LOGI(TAG, "UMS mode exited");
    return ESP_OK;
}

/**
 * @brief 启动Linux系统
 */
esp_err_t uboot_boot_linux(void)
{
    ESP_LOGI(TAG, "Booting Linux system...");

    esp_err_t ret = uboot_send_command(UBOOT_BOOT_COMMAND, NULL, 2000, NULL, 0);
    if (ret != ESP_OK) {
        set_last_error("Failed to send boot command");
        return ret;
    }

    ESP_LOGI(TAG, "Linux boot command sent");
    return ESP_OK;
}

/**
 * @brief 启动USB更新测试
 */
esp_err_t uboot_start_usb_update_test(void)
{
    ESP_LOGI(TAG, "Starting USB update test...");

    // 设置USB更新状态为进行中
    usb_otg_set_update_status(USB_UPDATE_IN_PROGRESS);

    esp_err_t ret;

    // 步骤1: Linux登录
    ret = uboot_linux_login();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Step 1 failed: Linux login");
        usb_otg_set_update_status(USB_UPDATE_ERROR);
        return ret;
    }

    // 步骤2: 系统重启
    ret = uboot_system_reboot();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Step 2 failed: System reboot");
        usb_otg_set_update_status(USB_UPDATE_ERROR);
        return ret;
    }

    // 步骤3: 进入U-Boot命令模式
    ret = uboot_enter_command_mode();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Step 3 failed: Enter U-Boot command mode");
        usb_otg_set_update_status(USB_UPDATE_ERROR);
        return ret;
    }

    // 步骤4: 启动UMS模式
    ret = uboot_start_ums_mode();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Step 4 failed: Start UMS mode");
        usb_otg_set_update_status(USB_UPDATE_ERROR);
        return ret;
    }

    // 步骤5: 初始化USB-OTG并检测文件
    ret = usb_otg_start_host_mode();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Step 5 failed: Start USB host mode");
        usb_otg_set_update_status(USB_UPDATE_ERROR);
        return ret;
    }

    // 等待一段时间让USB设备稳定
    vTaskDelay(pdMS_TO_TICKS(2000));

    // 步骤6: 执行文件检测（异步，由USB监控任务处理）
    ESP_LOGI(TAG, "USB update test initiated, waiting for file detection...");

    // 等待文件检测完成（最多等待10秒）
    for (int i = 0; i < 100; i++) {
        usb_update_status_t status = usb_otg_get_update_status();
        if (status == USB_UPDATE_SUCCESS || status == USB_UPDATE_ERROR) {
            break;
        }
        vTaskDelay(pdMS_TO_TICKS(100));
    }

    // 步骤7: 退出UMS模式
    uboot_exit_ums_mode();

    // 步骤8: 启动Linux
    uboot_boot_linux();

    usb_update_status_t final_status = usb_otg_get_update_status();
    if (final_status == USB_UPDATE_SUCCESS) {
        ESP_LOGI(TAG, "USB update test completed successfully");
        return ESP_OK;
    } else {
        ESP_LOGE(TAG, "USB update test failed");
        return ESP_FAIL;
    }
}

/**
 * @brief 获取最后一次操作的错误信息
 */
esp_err_t uboot_get_last_error(char* error_buffer, int buffer_size)
{
    if (error_buffer == NULL || buffer_size <= 0) {
        return ESP_ERR_INVALID_ARG;
    }

    strncpy(error_buffer, last_error_message, buffer_size - 1);
    error_buffer[buffer_size - 1] = '\0';

    return ESP_OK;
}

/**
 * @brief 去初始化U-Boot操作模块
 */
void uboot_operations_deinit(void)
{
    memset(last_error_message, 0, sizeof(last_error_message));
    ESP_LOGI(TAG, "U-Boot operations module deinitialized");
}
