#include "system_config.h"
#include "state_machine.h"
#include "at_commands.h"
#include "gpio_monitor.h"
#include "uart_passthrough.h"
#include "usb_otg.h"
#include "uboot_operations.h"
#include "error_handler.h"
#include "watchdog_config.h"
#include "esp_log.h"
#include "string.h"

static const char *TAG = "MAIN";

// 全局系统上下文
system_context_t g_system_ctx = {0};
soft_uart_t g_soft_uart = {0};

/**
 * @brief 初始化UART端口
 */
static esp_err_t init_uart_ports(void)
{
    ESP_LOGI(TAG, "Initializing UART ports...");
    
    // 初始化主控制串口 (UART0)
    const uart_config_t main_uart_config = {
        .baud_rate = 115200,
        .data_bits = UART_DATA_8_BITS,
        .parity = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE,
        .source_clk = UART_SCLK_DEFAULT,
    };
    
    esp_err_t ret = uart_driver_install(UART_MAIN_CONTROL, RX_BUF_SIZE * 2, TX_BUF_SIZE * 2, 0, NULL, 0);
    CHECK_ERROR_AND_RETURN(ret == ESP_OK, ERROR_UART_INIT_FAILED, "Failed to install main UART driver");
    
    ret = uart_param_config(UART_MAIN_CONTROL, &main_uart_config);
    CHECK_ERROR_AND_RETURN(ret == ESP_OK, ERROR_UART_INIT_FAILED, "Failed to configure main UART");
    
    ret = uart_set_pin(UART_MAIN_CONTROL, MAIN_CONTROL_TXD, MAIN_CONTROL_RXD, 
                       UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE);
    CHECK_ERROR_AND_RETURN(ret == ESP_OK, ERROR_UART_INIT_FAILED, "Failed to set main UART pins");
    
    // 初始化Debug串口 (UART1)
    const uart_config_t debug_uart_config = {
        .baud_rate = 115200,
        .data_bits = UART_DATA_8_BITS,
        .parity = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE,
        .source_clk = UART_SCLK_DEFAULT,
    };
    
    ret = uart_driver_install(UART_DEBUG, RX_BUF_SIZE * 2, TX_BUF_SIZE * 2, 0, NULL, 0);
    CHECK_ERROR_AND_RETURN(ret == ESP_OK, ERROR_UART_INIT_FAILED, "Failed to install debug UART driver");
    
    ret = uart_param_config(UART_DEBUG, &debug_uart_config);
    CHECK_ERROR_AND_RETURN(ret == ESP_OK, ERROR_UART_INIT_FAILED, "Failed to configure debug UART");
    
    ret = uart_set_pin(UART_DEBUG, DEBUG_TXD, DEBUG_RXD, 
                       UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE);
    CHECK_ERROR_AND_RETURN(ret == ESP_OK, ERROR_UART_INIT_FAILED, "Failed to set debug UART pins");
    
    ESP_LOGI(TAG, "UART ports initialized successfully");
    return ESP_OK;
}

/**
 * @brief 系统初始化
 */
static esp_err_t system_init(void)
{
    ESP_LOGI(TAG, "Starting system initialization...");
    ESP_LOGI(TAG, "System version: %s", SYSTEM_VERSION);
    
    // 初始化系统上下文
    memset(&g_system_ctx, 0, sizeof(g_system_ctx));
    memset(&g_soft_uart, 0, sizeof(g_soft_uart));
    
    // 1. 初始化错误处理模块
    esp_err_t ret = error_handler_init();
    CHECK_ERROR_AND_RETURN(ret == ESP_OK, ERROR_UNKNOWN, "Failed to initialize error handler");
    
    // 2. 初始化UART端口
    ret = init_uart_ports();
    if (ret != ESP_OK) {
        return ret;
    }
    
    // 3. 初始化状态机
    ret = state_machine_init();
    CHECK_ERROR_AND_RETURN(ret == ESP_OK, ERROR_UNKNOWN, "Failed to initialize state machine");
    
    // 4. 初始化AT指令处理模块
    ret = at_commands_init();
    CHECK_ERROR_AND_RETURN(ret == ESP_OK, ERROR_UNKNOWN, "Failed to initialize AT commands");
    
    // 5. 初始化GPIO监控模块
    ret = gpio_monitor_init();
    CHECK_ERROR_AND_RETURN(ret == ESP_OK, ERROR_GPIO_INIT_FAILED, "Failed to initialize GPIO monitor");
    
    // 6. 初始化串口透传模块
    ret = uart_passthrough_init();
    CHECK_ERROR_AND_RETURN(ret == ESP_OK, ERROR_UART_INIT_FAILED, "Failed to initialize UART passthrough");
    
    // 7. 初始化USB-OTG模块
    ret = usb_otg_init();
    CHECK_ERROR_AND_RETURN(ret == ESP_OK, ERROR_USB_INIT_FAILED, "Failed to initialize USB-OTG");
    
    // 8. 初始化U-Boot操作模块
    ret = uboot_operations_init();
    CHECK_ERROR_AND_RETURN(ret == ESP_OK, ERROR_UNKNOWN, "Failed to initialize U-Boot operations");
    
    ESP_LOGI(TAG, "System initialization completed successfully");
    return ESP_OK;
}

/**
 * @brief 启动所有任务
 */
static esp_err_t start_all_tasks(void)
{
    ESP_LOGI(TAG, "Starting all tasks...");
    
    // 1. 启动错误处理任务
    esp_err_t ret = error_handler_start_task();
    CHECK_ERROR_AND_RETURN(ret == ESP_OK, ERROR_TASK_CREATE_FAILED, "Failed to start error handler task");
    
    // 2. 启动GPIO监控任务
    ret = gpio_monitor_start_task();
    CHECK_ERROR_AND_RETURN(ret == ESP_OK, ERROR_TASK_CREATE_FAILED, "Failed to start GPIO monitor task");
    
    // 3. 启动串口透传任务
    ret = uart_passthrough_start_tasks();
    CHECK_ERROR_AND_RETURN(ret == ESP_OK, ERROR_TASK_CREATE_FAILED, "Failed to start UART passthrough tasks");
    
    // 4. 启动USB监控任务
    ret = usb_otg_start_monitor_task();
    CHECK_ERROR_AND_RETURN(ret == ESP_OK, ERROR_TASK_CREATE_FAILED, "Failed to start USB monitor task");
    
    // 5. 启动USB-OTG主机模式
    ret = usb_otg_start_host_mode();
    CHECK_ERROR_AND_RETURN(ret == ESP_OK, ERROR_USB_INIT_FAILED, "Failed to start USB host mode");
    
    ESP_LOGI(TAG, "All tasks started successfully");
    return ESP_OK;
}

/**
 * @brief AT指令处理任务
 */
void at_commands_task(void* pvParameters)
{
    ESP_LOGI(TAG, "AT commands task started");

    // 将任务添加到看门狗监控
    esp_err_t wdt_ret = watchdog_add_current_task("at_commands");
    if (wdt_ret != ESP_OK) {
        ESP_LOGW(TAG, "Failed to add AT commands task to watchdog, continuing anyway");
    }

    uint8_t rx_buffer[AT_CMD_MAX_LEN];
    at_command_t at_cmd;
    uint32_t wdt_reset_counter = 0;
    const uint32_t wdt_reset_interval = 50; // 每50次循环重置一次看门狗

    while (1) {
        // 定期重置看门狗
        wdt_reset_counter++;
        if (wdt_reset_counter >= wdt_reset_interval) {
            watchdog_reset();
            wdt_reset_counter = 0;
        }

        // 从主控制串口读取AT指令 - 使用较短超时避免长时间阻塞
        int rx_len = uart_read_bytes(UART_MAIN_CONTROL, rx_buffer, AT_CMD_MAX_LEN - 1, pdMS_TO_TICKS(50));

        if (rx_len > 0) {
            rx_buffer[rx_len] = '\0';
            ESP_LOGI(TAG, "Received AT command: %s", rx_buffer);

            // 接收到数据后立即重置看门狗
            watchdog_reset();

            // 解析AT指令
            esp_err_t ret = at_commands_parse((char*)rx_buffer, &at_cmd);
            if (ret == ESP_OK) {
                // 处理AT指令
                ret = at_commands_process(&at_cmd);
                if (ret != ESP_OK && ret != ESP_ERR_NOT_FINISHED) {
                    ESP_LOGE(TAG, "Failed to process AT command: %s", esp_err_to_name(ret));
                    LOG_ERROR(ERROR_AT_COMMAND_TIMEOUT, "AT command processing failed");
                }
            } else {
                ESP_LOGW(TAG, "Failed to parse AT command: %s", (char*)rx_buffer);
            }
        }

        vTaskDelay(pdMS_TO_TICKS(10));
    }
}

/**
 * @brief 主任务
 */
void main_task(void* pvParameters)
{
    ESP_LOGI(TAG, "Main task started");
    
    TickType_t last_wake_time = xTaskGetTickCount();
    TickType_t status_period = pdMS_TO_TICKS(10000); // 10秒状态报告周期
    if (status_period == 0) {
        status_period = 1; // 至少1个tick
    }
    
    while (1) {
        // 定期打印系统状态
        system_state_t current_state = state_machine_get_current_state();
        uint8_t gpio_status = gpio_monitor_get_status();
        usb_update_status_t usb_status = usb_otg_get_update_status();
        
        ESP_LOGI(TAG, "System Status - State: %s, GPIO: 0x%02X, USB: %d", 
                 state_machine_get_state_name(current_state), gpio_status, usb_status);
        
        // 检查系统健康状态
        bool is_healthy = error_handler_check_system_health();
        if (!is_healthy) {
            ESP_LOGW(TAG, "System health check failed");
        }
        
        // 等待下一个状态报告周期
        vTaskDelayUntil(&last_wake_time, status_period);
    }
}

/**
 * @brief 应用程序入口点
 */
void app_main(void)
{
    ESP_LOGI(TAG, "ESP32S3 Linux Board Test System Starting...");
    ESP_LOGI(TAG, "Version: %s", SYSTEM_VERSION);
    
    // 系统初始化
    esp_err_t ret = system_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "System initialization failed: %s", esp_err_to_name(ret));
        return;
    }
    
    // 启动所有任务
    ret = start_all_tasks();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start tasks: %s", esp_err_to_name(ret));
        return;
    }
    
    // 创建AT指令处理任务
    BaseType_t task_ret = xTaskCreate(
        at_commands_task,
        "at_commands",
        SYSTEM_TASK_STACK_SIZE,
        NULL,
        SYSTEM_TASK_PRIORITY,
        &g_system_ctx.main_task_handle
    );
    
    if (task_ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create AT commands task");
        LOG_ERROR(ERROR_TASK_CREATE_FAILED, "AT commands task creation failed");
        return;
    }
    
    // 创建主任务
    task_ret = xTaskCreate(
        main_task,
        "main_task",
        SYSTEM_TASK_STACK_SIZE,
        NULL,
        SYSTEM_TASK_PRIORITY - 1,
        NULL
    );
    
    if (task_ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create main task");
        LOG_ERROR(ERROR_TASK_CREATE_FAILED, "Main task creation failed");
        return;
    }
    
    ESP_LOGI(TAG, "System startup completed successfully");
    ESP_LOGI(TAG, "System is now in IDLE state, waiting for <AT_STAR> command...");
}
