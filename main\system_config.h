#ifndef SYSTEM_CONFIG_H
#define SYSTEM_CONFIG_H

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "freertos/semphr.h"
#include "driver/uart.h"
#include "driver/gpio.h"
#include "driver/dedic_gpio.h"
#include "hal/dedic_gpio_cpu_ll.h"
#include "esp_log.h"
#include "esp_err.h"
#include "esp_timer.h"

// 系统版本信息
#define SYSTEM_VERSION "GS068-01_v1.0"

// 功能开关
#define ENABLE_SOFTWARE_UART    1   // 暂时禁用软件UART以便调试栈溢出问题

// 缓冲区大小定义
#define RX_BUF_SIZE 1024
#define TX_BUF_SIZE 1024
#define AT_CMD_MAX_LEN 256
#define AT_RESPONSE_MAX_LEN 512

// UART端口定义
#define UART_MAIN_CONTROL   UART_NUM_0  // GPIO4/5 - 主控制串口，接收AT指令
#define UART_DEBUG          UART_NUM_1  // GPIO6/7 - Debug串口，连接Linux板Debug控制台
#define UART_PASSTHROUGH_1  UART_NUM_2  // GPIO15/16 - 通用串口1，数据透传
// UART3使用软件串口实现 GPIO17/18 - 通用串口2，数据透传

// UART引脚定义
#define MAIN_CONTROL_TXD    GPIO_NUM_4
#define MAIN_CONTROL_RXD    GPIO_NUM_5

#define DEBUG_TXD           GPIO_NUM_6
#define DEBUG_RXD           GPIO_NUM_7

#define PASSTHROUGH1_TXD    GPIO_NUM_15
#define PASSTHROUGH1_RXD    GPIO_NUM_16

#define PASSTHROUGH2_TXD    GPIO_NUM_17  // 软串口
#define PASSTHROUGH2_RXD    GPIO_NUM_18  // 软串口

// USB-OTG引脚定义
#define USB_OTG_DP          GPIO_NUM_19
#define USB_OTG_DM          GPIO_NUM_20

// GPIO监控引脚定义 (GPIO35-42, 8个引脚)
#define GPIO_MONITOR_COUNT  8
static const gpio_num_t gpio_monitor_pins[GPIO_MONITOR_COUNT] = {
    GPIO_NUM_35, GPIO_NUM_36, GPIO_NUM_37, GPIO_NUM_38,
    GPIO_NUM_39, GPIO_NUM_40, GPIO_NUM_41, GPIO_NUM_42
};

// 系统状态枚举
typedef enum {
    SYSTEM_STATE_IDLE,      // 初始状态，等待AT_STAR指令
    SYSTEM_STATE_TESTING    // 测试状态，执行各种测试任务
} system_state_t;

// AT指令类型枚举
typedef enum {
    AT_CMD_UNKNOWN,
    AT_CMD_STAR,                // <AT_STAR>
    AT_CMD_TEST_DEBUG,          // <AT_TEST_DEBUG>
    AT_CMD_GET_GPIO_STA,        // <AT_GET_GPIO_STA>
    AT_CMD_TEST_USB_UPDATE,     // <AT_TEST_USB_UPDATE>
    AT_CMD_GET_TEST_USB_UPDATE_STA  // <AT_GET_TEST_USB_UPDATE_STA>
} at_command_type_t;

// AT指令结构体
typedef struct {
    at_command_type_t type;
    char raw_command[AT_CMD_MAX_LEN];
    char response[AT_RESPONSE_MAX_LEN];
    bool processed;
    uint32_t timestamp;
} at_command_t;

// USB更新测试结果
typedef enum {
    USB_UPDATE_IDLE,
    USB_UPDATE_IN_PROGRESS,
    USB_UPDATE_SUCCESS,
    USB_UPDATE_ERROR
} usb_update_status_t;

// 系统全局状态结构体
typedef struct {
    system_state_t current_state;
    SemaphoreHandle_t state_mutex;
    
    // GPIO状态
    uint8_t gpio_status;  // 8位二进制状态
    SemaphoreHandle_t gpio_mutex;
    
    // USB更新测试状态
    usb_update_status_t usb_update_status;
    SemaphoreHandle_t usb_mutex;
    
    // 队列句柄
    QueueHandle_t at_command_queue;
    QueueHandle_t uart_passthrough_queue;
    
    // 任务句柄
    TaskHandle_t main_task_handle;
    TaskHandle_t gpio_monitor_task_handle;
    TaskHandle_t uart_passthrough_task_handle;
    TaskHandle_t usb_monitor_task_handle;
    
} system_context_t;

// 软串口结构体 - 基于Dedicated GPIO
typedef struct {
    gpio_num_t tx_pin;
    gpio_num_t rx_pin;
    QueueHandle_t tx_queue;
    QueueHandle_t rx_queue;
    TaskHandle_t tx_task_handle;
    TaskHandle_t rx_task_handle;
    bool initialized;

    // Dedicated GPIO相关
    dedic_gpio_bundle_handle_t tx_bundle;
    dedic_gpio_bundle_handle_t rx_bundle;
    uint32_t tx_mask;
    uint32_t rx_mask;

    // 波特率相关
    uint32_t baud_rate;
    uint32_t bit_time_us;

    // 统计信息
    uint32_t tx_bytes;
    uint32_t rx_bytes;
    uint32_t rx_errors;
} soft_uart_t;

// 全局系统上下文
extern system_context_t g_system_ctx;
extern soft_uart_t g_soft_uart;

// 常用宏定义
#define SYSTEM_TASK_STACK_SIZE      4096
#define GPIO_TASK_STACK_SIZE        2048
#define UART_TASK_STACK_SIZE        3072    // 增加软件UART任务栈大小
#define USB_TASK_STACK_SIZE         4096

#define SYSTEM_TASK_PRIORITY        5
#define GPIO_TASK_PRIORITY          4
#define UART_TASK_PRIORITY          3       // 提高UART任务优先级，避免看门狗超时
#define USB_TASK_PRIORITY           2

// 超时定义 (毫秒)
#define AT_COMMAND_TIMEOUT          30000   // 30秒
#define DEBUG_LOGIN_TIMEOUT         30000   // 30秒
#define UBOOT_COMMAND_TIMEOUT       10000   // 10秒
#define USB_DETECTION_TIMEOUT       5000    // 5秒

// AT指令响应字符串
#define AT_RESPONSE_STAR_OK         "<AT_STAR_OK>"
#define AT_RESPONSE_DEBUG_OK        "<AT_TEST_DEBUG_OK>"
#define AT_RESPONSE_DEBUG_ERROR     "<AT_TEST_DEBUG_ERROR>"
#define AT_RESPONSE_GPIO_PREFIX     "<AT_GET_GPIO_"
#define AT_RESPONSE_USB_UPDATE_OK   "<AT_GET_TEST_USB_UPDATE_OK>"
#define AT_RESPONSE_USB_UPDATE_ERROR "<AT_GET_TEST_USB_UPDATE_ERROR>"

// U-Boot相关字符串
#define UBOOT_AUTOBOOT_PROMPT       "Hit any key to stop autoboot:"
#define UBOOT_PROMPT                "uboot=>"
#define UBOOT_UMS_COMMAND           "ums 0 mmc 2"
#define UBOOT_BOOT_COMMAND          "boot"
#define LINUX_ROOT_PROMPT           "#"

// USB文件检测
#define USB_TARGET_FILE1            "Image"
#define USB_TARGET_FILE2            "imx8mp-evk.dtb"

#endif // SYSTEM_CONFIG_H
